import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import 'package:rozana/routes/app_router.dart';
import 'package:rozana/core/utils/text_field_manager.dart';
import 'package:rozana/widgets/custom_textfield.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../core/utils/app_validators.dart';
import '../../../../data/models/adress_model.dart';
import '../../../../widgets/custom_button.dart';
import '../../bloc/location bloc/location_bloc.dart';

class AddressFormScreen extends StatefulWidget {
  final AddressModel? address;
  final bool fromCart;

  const AddressFormScreen({
    super.key,
    this.address,
    this.fromCart = false,
  });

  @override
  State<AddressFormScreen> createState() => _AddressFormScreenState();
}

class _AddressFormScreenState extends State<AddressFormScreen> {
  final _formKey = GlobalKey<FormState>();

  final _addressLine1Controller = TextFieldManager();
  final _landmarkController = TextFieldManager();
  final _cityController = TextFieldManager();
  final _stateController = TextFieldManager();
  final _pincodeController = TextFieldManager();
  final _searchController = TextFieldManager();

  bool _isDefault = false;
  String _addressType = 'home';

  double _latitude = 0.0;
  double _longitude = 0.0;

  @override
  void initState() {
    super.initState();

    // Add listeners to text field controllers to sync with BLoC
    _addressLine1Controller.controller.addListener(() {
      context.read<LocationBloc>().add(
            LocationEvent.updateFormField(
                'addressLine1', _addressLine1Controller.text),
          );
    });

    _landmarkController.controller.addListener(() {
      context.read<LocationBloc>().add(
            LocationEvent.updateFormField('landmark', _landmarkController.text),
          );
    });

    _cityController.controller.addListener(() {
      context.read<LocationBloc>().add(
            LocationEvent.updateFormField('city', _cityController.text),
          );
    });

    _stateController.controller.addListener(() {
      context.read<LocationBloc>().add(
            LocationEvent.updateFormField('state', _stateController.text),
          );
    });

    _pincodeController.controller.addListener(() {
      context.read<LocationBloc>().add(
            LocationEvent.updateFormField('pincode', _pincodeController.text),
          );
    });

    if (widget.address != null) {
      _populateFormWithAddress(widget.address!);
      // Initialize form data in BLoC
      _initializeFormData();
    } else {
      // For new addresses, try to get current location
      context
          .read<LocationBloc>()
          .add(const LocationEvent.getCurrentLocation());
    }
  }

  @override
  void dispose() {
    _addressLine1Controller.dispose();
    _landmarkController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _pincodeController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _populateFormWithAddress(AddressModel address) {
    _addressLine1Controller.text = address.addressLine1 ?? '';
    _landmarkController.text = address.landmark ?? '';
    _cityController.text = address.city ?? '';
    _stateController.text = address.state ?? '';
    _pincodeController.text = address.pincode ?? '';
    _isDefault = address.isDefault ?? false;
    _addressType = address.addressType ?? '';
    _latitude = (address.latitude ?? 0).toDouble();
    _longitude = (address.longitude ?? 0).toDouble();
  }

  void _initializeFormData() {
    final locationBloc = context.read<LocationBloc>();

    // Initialize form data in BLoC
    locationBloc.add(LocationEvent.updateFormField(
        'addressLine1', _addressLine1Controller.text));
    locationBloc.add(
        LocationEvent.updateFormField('landmark', _landmarkController.text));
    locationBloc
        .add(LocationEvent.updateFormField('city', _cityController.text));
    locationBloc
        .add(LocationEvent.updateFormField('state', _stateController.text));
    locationBloc
        .add(LocationEvent.updateFormField('pincode', _pincodeController.text));
    locationBloc.add(LocationEvent.setAddressType(_addressType));
    locationBloc.add(LocationEvent.setDefaultFlag(_isDefault));
  }

  Future<void> _navigateToLocationSelection() async {
    final result = await context.push(
      RouteNames.mapForEditAddress,
      extra: {widget.address},
    );

    if (result != null && result is Map<String, dynamic>) {
      final latitude = result['latitude'] as double?;
      final longitude = result['longitude'] as double?;

      if (latitude != null && longitude != null) {
        setState(() {
          _latitude = latitude;
          _longitude = longitude;
        });
      }
    }
  }

  void _saveAddress() {
    // Validate that coordinates are set (not default 0.0, 0.0)
    if (_latitude == 0.0 && _longitude == 0.0) {
      _showSnackBar(
          'Please select a location on the map or allow location access');
      return;
    }

    final addressId =
        widget.address?.id ?? DateTime.now().millisecondsSinceEpoch.toString();

    final address = AddressModel(
      id: addressId,
      fullAddress: _buildFullAddress(),
      addressLine1: _addressLine1Controller.text,
      landmark:
          _landmarkController.text.isNotEmpty ? _landmarkController.text : null,
      city: _cityController.text,
      state: _stateController.text,
      pincode: _pincodeController.text,
      latitude: _latitude,
      longitude: _longitude,
      addressType: _addressType,
      isDefault: _isDefault,
    );

    // Use BLoC to save address
    if (widget.address != null) {
      context.read<LocationBloc>().add(LocationEvent.editAddress(address));
    } else {
      context.read<LocationBloc>().add(LocationEvent.addAddress(address));
    }
  }

  String _buildFullAddress() {
    final parts = [
      _addressLine1Controller.text,
      _landmarkController.text,
      _cityController.text,
      _stateController.text,
      _pincodeController.text,
    ];

    return parts.where((part) => part.isNotEmpty).join(', ');
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<LocationBloc, LocationState>(
      listener: (context, state) {
        state.when(
          initial: () {},
          loading: () {},
          loaded: (address) {},
          error: (message) => _showSnackBar(message),
          addressesLoaded: (addresses, defaultAddressId) {},
          addressSaved: (address) {
            // Navigate back after successful save
            if (mounted) {
              if (widget.fromCart) {
                context.go(RouteNames.cart);
              } else {
                context.go('/'); // Navigate to home instead of just popping
              }
            }
          },
          addressDeleted: (addressId) {},
          defaultAddressSet: (addressId) {},
          formState: (formData) {
            // Update form fields from BLoC state
            setState(() {
              _latitude = formData.latitude;
              _longitude = formData.longitude;
            });
          },
          formError: (errors) {
            // Handle form validation errors
            errors.forEach((field, error) {
              switch (field) {
                case 'addressLine1':
                  _addressLine1Controller.throwError(error);
                  break;
                case 'city':
                  _cityController.throwError(error);
                  break;
                case 'state':
                  _stateController.throwError(error);
                  break;
                case 'pincode':
                  _pincodeController.throwError(error);
                  break;
              }
            });
          },
          mapState: (mapData) {},
          locationDetected: (latitude, longitude, address) {
            setState(() {
              _latitude = latitude;
              _longitude = longitude;
            });
          },
          searchResults: (results) {},
        );
      },
      child: BlocBuilder<LocationBloc, LocationState>(
        builder: (context, state) {
          final isLoading = state.maybeWhen(
            loading: () => true,
            orElse: () => false,
          );

          return Scaffold(
            appBar: AppBar(
              title: Text('Add Address Details'),
              backgroundColor: AppColors.primaryAverage,
              foregroundColor: Colors.white,
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: AppColors.primaryAverage,
                statusBarIconBrightness: Brightness.light,
              ),
            ),
            body: _buildForm(isLoading),
          );
        },
      ),
    );
  }

  Widget _buildForm(bool isLoading) {
    return Form(
      key: _formKey,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Select location on map button
          AppButton(
            text: 'Select Location on Map',
            onPressed: _navigateToLocationSelection,
            isOutlined: true,
            prefixIcon: const Icon(Icons.map, size: 18),
          ),

          const SizedBox(height: 24),

          // Address type selector
          Text(
            'Address Type',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          _buildAddressTypeSelector(),
          const SizedBox(height: 24),

          // Address form fields
          ValueListenableBuilder(
              valueListenable: _addressLine1Controller.errorText,
              builder: (context, error, _) {
                return Titledfield(
                  title: 'Address Line',
                  field: CustomTextField(
                    hintText: 'House/Flat No., Building, Street',
                    controller: _addressLine1Controller.controller,
                    // focusNode: _addressLine1Controller.focusNode,
                    decoration: InputDecoration(
                      prefixIcon: const Icon(Icons.home),
                    ),
                  ),
                  errorText: error,
                );
              }),
          const SizedBox(height: 16),

          Titledfield(
            title: 'Landmark (Optional)',
            field: CustomTextField(
                hintText: 'Nearby landmark',
                controller: _landmarkController.controller,
                // focusNode: _landmarkController.focusNode,
                decoration: InputDecoration(
                  prefixIcon: const Icon(Icons.location_on),
                )),
          ),
          const SizedBox(height: 16),

          ValueListenableBuilder(
              valueListenable: _cityController.errorText,
              builder: (context, error, _) {
                return Titledfield(
                  title: 'City',
                  field: CustomTextField(
                    hintText: 'City',
                    controller: _cityController.controller,
                    // focusNode: _cityController.focusNode,
                    decoration: InputDecoration(
                      prefixIcon: const Icon(Icons.location_city),
                    ),
                  ),
                  errorText: error,
                );
              }),
          const SizedBox(height: 16),

          ValueListenableBuilder(
              valueListenable: _stateController.errorText,
              builder: (context, error, _) {
                return Titledfield(
                  title: 'State',
                  field: CustomTextField(
                    hintText: 'State',
                    controller: _stateController.controller,
                    // focusNode: _stateController.focusNode,
                    decoration: InputDecoration(
                      prefixIcon: const Icon(Icons.map),
                    ),
                  ),
                  errorText: error,
                );
              }),
          const SizedBox(height: 16),

          ValueListenableBuilder(
              valueListenable: _pincodeController.errorText,
              builder: (context, error, _) {
                return Titledfield(
                  title: 'Pincode',
                  field: CustomTextField(
                      hintText: 'Pincode',
                      controller: _pincodeController.controller,
                      // focusNode: _pincodeController.focusNode,
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(6),
                      ],
                      decoration: InputDecoration(
                        prefixIcon: const Icon(Icons.pin_drop),
                      )),
                  errorText: error,
                );
              }),
          const SizedBox(height: 24),

          // Default address checkbox
          Row(
            children: [
              Checkbox(
                value: _isDefault,
                onChanged: (value) {
                  setState(() {
                    _isDefault = value ?? false;
                  });
                  context
                      .read<LocationBloc>()
                      .add(LocationEvent.setDefaultFlag(value ?? false));
                },
                activeColor: AppColors.primaryAverage,
              ),
              const Text('Set as default address'),
            ],
          ),
          const SizedBox(height: 24),

          // Save button
          AppButton(
            text: 'Save Address',
            onPressed: () {
              ValidationState addressValidationState =
                  AppValidator.emptyStringValidator(
                      _addressLine1Controller.text, 'Please enter address');

              ValidationState cityValidationState =
                  AppValidator.emptyStringValidator(
                      _cityController.text, 'Please enter city');
              ValidationState stateValidationState =
                  AppValidator.emptyStringValidator(
                      _stateController.text, 'Please enter state');
              ValidationState pincodeValidationState =
                  AppValidator.emptyStringValidator(
                      _pincodeController.text, 'Please enter pincode',
                      minLength: 6,
                      lengthMessage: 'Please enter a valid 6-digit pincode');

              if (!addressValidationState.valid) {
                _addressLine1Controller
                    .throwError(addressValidationState.message ?? '');
                return;
              }
              _addressLine1Controller.throwError('');
              if (!cityValidationState.valid) {
                _cityController.throwError(cityValidationState.message ?? '');
                return;
              }
              _cityController.throwError('');
              if (!stateValidationState.valid) {
                _stateController.throwError(stateValidationState.message ?? '');
                return;
              }
              _stateController.throwError('');
              if (!pincodeValidationState.valid) {
                _pincodeController
                    .throwError(pincodeValidationState.message ?? '');
                return;
              }
              _pincodeController.throwError('');
              _saveAddress();
            },
            isLoading: isLoading,
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildAddressTypeSelector() {
    return Row(
      children: [
        _buildAddressTypeOption('home', 'Home', Icons.home),
        const SizedBox(width: 16),
        _buildAddressTypeOption('work', 'Work', Icons.work),
        const SizedBox(width: 16),
        _buildAddressTypeOption('other', 'Other', Icons.place),
      ],
    );
  }

  Widget _buildAddressTypeOption(String type, String label, IconData icon) {
    final isSelected = _addressType == type;

    return Expanded(
      child: InkWell(
        onTap: () {
          setState(() {
            _addressType = type;
          });
          context.read<LocationBloc>().add(LocationEvent.setAddressType(type));
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? AppColors.primary : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
            color: isSelected
                ? AppColors.primary.withValues(alpha: 0.1)
                : Colors.transparent,
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: isSelected ? AppColors.primary : Colors.grey,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? AppColors.primary : Colors.grey.shade700,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
