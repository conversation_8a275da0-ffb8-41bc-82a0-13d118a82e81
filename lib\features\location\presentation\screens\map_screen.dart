import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/routes/app_router.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../widgets/custom_button.dart';
import '../../bloc/location bloc/location_bloc.dart';

class MapScreen extends StatefulWidget {
  final AddressModel? address;
  final Function(double latitude, double longitude)? onLocationSelected;
  final bool fromCart;
  const MapScreen({
    super.key,
    this.address,
    this.fromCart = false,
    this.onLocationSelected,
  });

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  GoogleMapController? _mapController;
  double? _currentLatitude;
  double? _currentLongitude;
  String _currentAddress = 'Loading address...';

  // Add search controller
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeLocation();
  }

  void _initializeLocation() {
    final locationBloc = context.read<LocationBloc>();

    if (widget.address != null &&
        widget.address!.latitude != null &&
        widget.address!.longitude != null) {
      // Set location from existing address
      _currentLatitude = (widget.address!.latitude as double);
      _currentLongitude = (widget.address!.longitude as double);
      locationBloc.add(LocationEvent.getAddressFromCoordinates(
        _currentLatitude!,
        _currentLongitude!,
      ));
    } else {
      // Get current location
      locationBloc.add(const LocationEvent.getCurrentLocation());
    }
  }

  void _onCameraMove(CameraPosition position) {
    // Update coordinates as map moves
    _currentLatitude = position.target.latitude;
    _currentLongitude = position.target.longitude;
  }

  void _onCameraIdle() {
    if (_currentLatitude != null && _currentLongitude != null) {
      context.read<LocationBloc>().add(
            LocationEvent.getAddressFromCoordinates(
                _currentLatitude!, _currentLongitude!),
          );
    }
  }

  void _getCurrentLocation() {
    context.read<LocationBloc>().add(const LocationEvent.getCurrentLocation());
  }

  void _selectLocation() {
    if (_currentLatitude == null || _currentLongitude == null) return;

    if (widget.onLocationSelected != null) {
      widget.onLocationSelected!(_currentLatitude!, _currentLongitude!);
      // If there's a callback, just call it and return
      if (mounted) {
        context.pop({
          'latitude': _currentLatitude!,
          'longitude': _currentLongitude!,
        });
      }
      return;
    }

    // Use BLoC to select location on map
    context.read<LocationBloc>().add(
          LocationEvent.selectLocationOnMap(
              _currentLatitude!, _currentLongitude!),
        );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _searchAndMoveToLocation(String query) {
    if (query.trim().isEmpty) return;
    context.read<LocationBloc>().add(LocationEvent.searchLocation(query));
  }

  void _navigateToAddressForm(LocationMapData mapData) {
    // Create address model from map data
    final addressModel = AddressModel(
      id: widget.address?.id, // preserve id if editing
      fullAddress: mapData.address,
      addressLine1: '', // Will be filled in the form
      city: '', // Will be filled in the form
      state: '', // Will be filled in the form
      pincode: '', // Will be filled in the form
      latitude: mapData.latitude,
      longitude: mapData.longitude,
      addressType: widget.address?.addressType ?? 'home',
      isDefault: widget.address?.isDefault ?? false,
    );

    if (mounted) {
      // Navigate to address form screen
      context.go(RouteNames.editAddress,
          extra: {'address': addressModel, 'fromCart': widget.fromCart});
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<LocationBloc, LocationState>(
      listener: (context, state) {
        state.when(
          initial: () {},
          loading: () {},
          loaded: (address) {},
          error: (message) => _showSnackBar(message),
          addressesLoaded: (addresses, defaultAddressId) {},
          addressSaved: (address) {},
          addressDeleted: (addressId) {},
          defaultAddressSet: (addressId) {},
          formState: (formData) {},
          formError: (errors) {},
          mapState: (mapData) {
            setState(() {
              _currentLatitude = mapData.latitude;
              _currentLongitude = mapData.longitude;
              _currentAddress = mapData.address;
            });
            if (mapData.latitude != 0.0 && mapData.longitude != 0.0) {
              _mapController?.animateCamera(
                CameraUpdate.newCameraPosition(
                  CameraPosition(
                    target: LatLng(mapData.latitude, mapData.longitude),
                    zoom: 16,
                  ),
                ),
              );

              // Navigate to address form when location is selected
              if (!mapData.isLoading) {
                _navigateToAddressForm(mapData);
              }
            }
          },
          locationDetected: (latitude, longitude, address) {
            setState(() {
              _currentLatitude = latitude;
              _currentLongitude = longitude;
              _currentAddress = address;
            });
            _mapController?.animateCamera(
              CameraUpdate.newCameraPosition(
                CameraPosition(
                  target: LatLng(latitude, longitude),
                  zoom: 16,
                ),
              ),
            );
          },
          searchResults: (results) {
            if (results.isNotEmpty) {
              final result = results.first;
              setState(() {
                _currentLatitude = (result.latitude ?? 0.0).toDouble();
                _currentLongitude = (result.longitude ?? 0.0).toDouble();
                _currentAddress = result.fullAddress ?? 'Location found';
              });
              _mapController?.animateCamera(
                CameraUpdate.newCameraPosition(
                  CameraPosition(
                    target: LatLng(_currentLatitude!, _currentLongitude!),
                    zoom: 16,
                  ),
                ),
              );
            } else {
              _showSnackBar('Location not found');
            }
          },
        );
      },
      child: BlocBuilder<LocationBloc, LocationState>(
        builder: (context, state) {
          if (_currentLatitude == null || _currentLongitude == null) {
            return const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            );
          }

          final isLoading = state.maybeWhen(
            loading: () => true,
            mapState: (mapData) => mapData.isLoading,
            orElse: () => false,
          );

          return Scaffold(
            appBar: AppBar(
              title: const Text('Select Location'),
              backgroundColor: AppColors.primaryAverage,
              foregroundColor: Colors.white,
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: AppColors.primary,
                statusBarIconBrightness: Brightness.light,
              ),
              actions: [
                IconButton(
                  onPressed: isLoading ? null : _getCurrentLocation,
                  icon: isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.my_location),
                  tooltip: 'Get Current Location',
                ),
              ],
            ),
            body: Stack(
              children: [
                // Google Map
                GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: LatLng(_currentLatitude!, _currentLongitude!),
                    zoom: 16,
                  ),
                  onMapCreated: (controller) {
                    _mapController = controller;
                  },
                  onCameraMove: _onCameraMove,
                  onCameraIdle: _onCameraIdle,
                  myLocationEnabled: true,
                  myLocationButtonEnabled: false, // We have our own button
                  zoomControlsEnabled: true,
                  mapToolbarEnabled: false,
                ),

                // Search bar at the top
                Positioned(
                  top: 20,
                  left: 16,
                  right: 16,
                  child: Material(
                    elevation: 4,
                    borderRadius: BorderRadius.circular(8),
                    child: TextField(
                      controller: _searchController,
                      textInputAction: TextInputAction.search,
                      onSubmitted: _searchAndMoveToLocation,
                      decoration: InputDecoration(
                        hintText: 'Search for a place or address',
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: isLoading
                            ? const Padding(
                                padding: EdgeInsets.all(10),
                                child: SizedBox(
                                  width: 16,
                                  height: 16,
                                  child:
                                      CircularProgressIndicator(strokeWidth: 2),
                                ),
                              )
                            : (_searchController.text.isNotEmpty
                                ? IconButton(
                                    icon: const Icon(Icons.clear),
                                    onPressed: () {
                                      _searchController.clear();
                                    },
                                  )
                                : null),
                        border: InputBorder.none,
                        contentPadding:
                            const EdgeInsets.symmetric(vertical: 14),
                      ),
                    ),
                  ),
                ),

                // Static marker in center
                const Center(
                  child: Icon(
                    Icons.location_pin,
                    size: 40,
                    color: AppColors.primaryAverage,
                  ),
                ),

                // Address info card
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 34),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(16)),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 10,
                          offset: Offset(0, -2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text(
                          'Selected Location',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primaryAverage,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _currentAddress,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 16),
                        CustomButton(
                          text: 'Select This Location',
                          onPressed: _selectLocation,
                          backgroundColor: AppColors.primaryAverage,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
